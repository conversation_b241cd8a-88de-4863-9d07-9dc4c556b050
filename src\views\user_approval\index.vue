<script setup lang="ts">
import { initRouter } from "@/router/utils";
import { storageLocal } from "@pureadmin/utils";
import { type CSSProperties, ref, computed } from "vue";
import { useUserStoreHook } from "@/store/modules/user";
import { usePermissionStoreHook } from "@/store/modules/permission";
defineOptions({
  name: "PermissionPage"
});
const elStyle = computed((): CSSProperties => {
  return {
    width: "85vw",
    justifyContent: "start"
  };
});

const username = ref(useUserStoreHook()?.username);

const options = [
  {
    value: "admin",
    label: "管理员角色"
  },
  {
    value: "common",
    label: "普通角色"
  }
];
interface User {
  date: string;
  name: string;
  address: string;
}
const tableData: User[] = [
  {
    date: "2016-05-04",
    name: "<PERSON><PERSON><PERSON>",
    address: "Lohrbergstr. 86c, <PERSON><PERSON><PERSON>, Saarland"
  },
  {
    date: "2016-05-03",
    name: "<PERSON>",
    address: "760 A Street, South Frankfield, Illinois"
  },
  {
    date: "2016-05-02",
    name: "<PERSON>",
    address: "Arnold-Ohletz-Str. 41a, Alt Malinascheid, Thüringen"
  },
  {
    date: "2016-05-01",
    name: "Margie Smith",
    address: "23618 Windsor Drive, West Ricardoview, Idaho"
  }
];
function onChange() {
  useUserStoreHook()
    .loginByUsername({ username: username.value, password: "admin123" })
    .then(res => {
      if (res.success) {
        storageLocal().removeItem("async-routes");
        usePermissionStoreHook().clearAllCachePage();
        initRouter();
      }
    });
}
</script>

<template>
  <div>
    <el-card shadow="never" :style="elStyle">
      <el-select v-model="username" class="w-[160px]!" @change="onChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-card>
    <el-card shadow="never" :style="elStyle" style="margin-top: 10px">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column label="Date" width="120">
          <template #default="scope">{{ scope.row.date }}</template>
        </el-table-column>
        <el-table-column property="name" label="Name" width="120" />
        <el-table-column
          property="address"
          label="use show-overflow-tooltip"
          width="240"
          show-overflow-tooltip
        />
        <el-table-column property="address" label="address" />
      </el-table>
    </el-card>
  </div>
</template>
