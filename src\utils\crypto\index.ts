import { sm2, sm3, sm4 } from "sm-crypto";
import { CRYPTO_CONFIG } from "./config";

/**
 * 国密加密工具类
 * 支持 SM2、SM3、SM4 加密算法
 */
export class SMCrypto {
  /**
   * SM2 加密
   * @param data 要加密的数据
   * @param publicKey 公钥（可选，不传则使用配置中的公钥）
   * @returns 加密后的数据
   */
  static sm2Encrypt(data: string, publicKey?: string): string {
    try {
      const key = publicKey || CRYPTO_CONFIG.SM2_PUBLIC_KEY;
      return sm2.doEncrypt(data, key, 1); // 1表示C1C3C2模式
    } catch (error) {
      console.error("SM2加密失败:", error);
      throw new Error("SM2加密失败");
    }
  }

  /**
   * SM2 解密
   * @param encryptedData 加密的数据
   * @param privateKey 私钥（可选，不传则使用配置中的私钥）
   * @returns 解密后的数据
   */
  static sm2Decrypt(encryptedData: string, privateKey?: string): string {
    try {
      const key = privateKey || CRYPTO_CONFIG.SM2_PRIVATE_KEY;
      return sm2.doDecrypt(encryptedData, key, 1); // 1表示C1C3C2模式
    } catch (error) {
      console.error("SM2解密失败:", error);
      throw new Error("SM2解密失败");
    }
  }

  /**
   * SM3 哈希
   * @param data 要哈希的数据
   * @returns 哈希值
   */
  static sm3Hash(data: string): string {
    try {
      return sm3(data);
    } catch (error) {
      console.error("SM3哈希失败:", error);
      throw new Error("SM3哈希失败");
    }
  }

  /**
   * SM4 加密
   * @param data 要加密的数据
   * @param key 密钥（可选，不传则使用配置中的密钥）
   * @returns 加密后的数据
   */
  static sm4Encrypt(data: string, key?: string): string {
    try {
      const encryptKey = key || CRYPTO_CONFIG.SM4_KEY;
      return sm4.encrypt(data, encryptKey);
    } catch (error) {
      console.error("SM4加密失败:", error);
      throw new Error("SM4加密失败");
    }
  }

  /**
   * SM4 解密
   * @param encryptedData 加密的数据
   * @param key 密钥（可选，不传则使用配置中的密钥）
   * @returns 解密后的数据
   */
  static sm4Decrypt(encryptedData: string, key?: string): string {
    try {
      const decryptKey = key || CRYPTO_CONFIG.SM4_KEY;
      return sm4.decrypt(encryptedData, decryptKey);
    } catch (error) {
      console.error("SM4解密失败:", error);
      throw new Error("SM4解密失败");
    }
  }

  /**
   * 对请求数据进行加密（推荐使用SM4对称加密，性能更好）
   * @param data 请求数据
   * @returns 加密后的数据
   */
  static encryptRequestData(data: any): string {
    try {
      const jsonString = typeof data === "string" ? data : JSON.stringify(data);
      return this.sm4Encrypt(jsonString);
    } catch (error) {
      console.error("请求数据加密失败:", error);
      throw error;
    }
  }

  /**
   * 对响应数据进行解密
   * @param encryptedData 加密的响应数据
   * @returns 解密后的数据
   */
  static decryptResponseData(encryptedData: string): any {
    try {
      const decryptedString = this.sm4Decrypt(encryptedData);
      return JSON.parse(decryptedString);
    } catch (error) {
      console.error("响应数据解密失败:", error);
      throw error;
    }
  }
}

// 导出默认实例
export default SMCrypto;
