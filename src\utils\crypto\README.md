# 国密加密集成说明

本项目已集成国密（SM）加密算法，支持 SM2、SM3、SM4 加密标准，可用于 API 请求/响应的自动加密解密。

## 功能特性

- ✅ **SM2 非对称加密**: 用于密钥交换和重要数据加密
- ✅ **SM3 哈希算法**: 用于数据完整性校验
- ✅ **SM4 对称加密**: 用于高性能数据传输加密
- ✅ **自动请求/响应加密**: HTTP 拦截器自动处理
- ✅ **灵活配置**: 支持白名单/黑名单控制
- ✅ **环境变量配置**: 支持不同环境使用不同密钥

## 快速开始

### 1. 环境配置

在 `.env.development` 或 `.env.production` 中配置：

```bash
# 是否启用加密
VITE_ENABLE_ENCRYPTION = true

# SM4 密钥（32位十六进制字符串）
VITE_SM4_KEY = 0123456789abcdeffedcba9876543210

# SM2 公钥（可选，用于非对称加密）
VITE_SM2_PUBLIC_KEY = your_sm2_public_key

# SM2 私钥（可选，注意安全性）
VITE_SM2_PRIVATE_KEY = your_sm2_private_key
```

### 2. 自动加密使用

HTTP 请求会根据配置自动加密/解密：

```typescript
import { http } from "@/utils/http";

// 这个请求会自动加密（如果接口在白名单中）
const response = await http.post("/api/user/login", {
  username: "admin",
  password: "123456"
});

// 响应数据会自动解密
console.log(response); // 已解密的数据
```

### 3. 手动控制加密

```typescript
// 强制不加密
const response = await http.post("/api/public/data", data, {
  headers: {
    "X-Encrypt": "false"
  }
});

// 强制加密（即使不在白名单中）
const response = await http.post("/api/data", data, {
  headers: {
    "X-Encrypt": "true"
  }
});
```

### 4. 直接使用加密工具

```typescript
import SMCrypto from "@/utils/crypto";

// SM4 对称加密（推荐用于数据传输）
const encrypted = SMCrypto.sm4Encrypt("hello world");
const decrypted = SMCrypto.sm4Decrypt(encrypted);

// SM2 非对称加密
const sm2Encrypted = SMCrypto.sm2Encrypt("sensitive data");
const sm2Decrypted = SMCrypto.sm2Decrypt(sm2Encrypted);

// SM3 哈希
const hash = SMCrypto.sm3Hash("data to hash");
```

## 配置说明

### 加密策略配置

在 `src/utils/crypto/config.ts` 中配置加密策略：

```typescript
export const CRYPTO_CONFIG = {
  // 需要加密的接口白名单
  ENCRYPT_WHITELIST: [
    "/api/user/login",
    "/api/user/register",
    "/api/sensitive-data"
  ],

  // 不需要加密的接口黑名单
  ENCRYPT_BLACKLIST: ["/api/public", "/api/health", "/refresh-token"]
};
```

### 加密判断逻辑

1. 如果全局禁用加密 (`VITE_ENABLE_ENCRYPTION = false`)，所有接口都不加密
2. 如果接口在黑名单中，不加密
3. 如果设置了白名单，只加密白名单中的接口
4. 如果没有设置白名单，默认加密所有接口（除了黑名单）
5. 可以通过请求头 `X-Encrypt: false` 临时跳过加密

## 安全建议

### 生产环境配置

1. **密钥管理**:

   - 不要在前端代码中硬编码密钥
   - 使用环境变量或从服务器动态获取密钥
   - 定期轮换密钥

2. **SM2 密钥对**:

   - 私钥不应该存储在前端
   - 考虑使用服务器端加密，前端只做解密

3. **传输安全**:
   - 结合 HTTPS 使用
   - 考虑添加时间戳和签名防重放攻击

### 开发环境调试

```bash
# 开发环境可以禁用加密方便调试
VITE_ENABLE_ENCRYPTION = false
```

## API 参考

### SMCrypto 类

#### 静态方法

- `sm2Encrypt(data: string, publicKey?: string): string` - SM2 加密
- `sm2Decrypt(encryptedData: string, privateKey?: string): string` - SM2 解密
- `sm3Hash(data: string): string` - SM3 哈希
- `sm4Encrypt(data: string, key?: string): string` - SM4 加密
- `sm4Decrypt(encryptedData: string, key?: string): string` - SM4 解密
- `encryptRequestData(data: any): string` - 请求数据加密（推荐）
- `decryptResponseData(encryptedData: string): any` - 响应数据解密（推荐）

### 配置函数

- `shouldEncrypt(url: string): boolean` - 判断接口是否需要加密

## 特殊接口处理

### 验证码接口

`getGenerateCode` 等验证码接口已自动加入黑名单，不会被加密：

- `/easyzhipin-admin/login/generateCode` - 图形验证码
- `/generateCode` - 通用验证码接口

这些接口通常返回图片数据或特殊格式，不适合加密处理。

### GET 请求

所有 GET 请求都不会被加密，因为：

1. GET 请求的数据在 URL 参数中，加密意义不大
2. 验证码、配置文件等 GET 请求通常不包含敏感数据
3. 避免对缓存和 CDN 造成影响

### 文件上传

包含 `multipart/form-data` 的文件上传请求不会被自动加密，需要手动处理。

## 常见问题

### Q: 为什么 getGenerateCode 请求没有被加密？

A: 验证码接口已加入黑名单，且 GET 请求默认不加密。这是正确的行为。

### Q: 加密后请求失败？

A: 检查服务器端是否支持解密，确保密钥一致。

### Q: 如何调试加密问题？

A: 设置 `VITE_ENABLE_ENCRYPTION = false` 或使用 `X-Encrypt: false` 请求头。

### Q: 性能影响？

A: SM4 对称加密性能较好，对于大数据量建议在服务器端处理。

### Q: 密钥安全？

A: 生产环境建议从服务器动态获取密钥，不要硬编码在前端。

## 更新日志

- v1.0.0: 初始版本，支持 SM2/SM3/SM4 算法
- 集成 HTTP 拦截器自动加密/解密
- 支持灵活的白名单/黑名单配置
