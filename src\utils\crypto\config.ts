/**
 * 国密加密配置文件
 * 在生产环境中，这些密钥应该从环境变量或安全的配置服务中获取
 */

// 从环境变量获取密钥，如果没有则使用默认值（仅用于开发环境）
export const CRYPTO_CONFIG = {
  // SM2 公钥（实际使用时应该从服务器获取或配置文件中读取）
  SM2_PUBLIC_KEY:
    import.meta.env.VITE_SM2_PUBLIC_KEY ||
    "04298364a5f7e7b4c2f8b1a3d9e6f0c8b5a2d7e4f1c8b5a2d9e6f3c0b7a4d1e8f5c2b9a6d3e0f7c4b1a8e5d2f9c6b3a0d7e4f1c8b5a2d9e6f3c0b7a4d1e8f5c2b9a6d3e0f7c4b1a8e5d2f9c6b3a0d7e4f1c8b5a2d9e6f3c0",

  // SM2 私钥（实际使用时应该从安全的地方获取，不应该暴露在前端代码中）
  SM2_PRIVATE_KEY:
    import.meta.env.VITE_SM2_PRIVATE_KEY ||
    "128B2FA8BD433C6C068C8D803DFF79792A519A55171B1B650C23661D15897263",

  // SM4 密钥（实际使用时应该从服务器动态获取）
  SM4_KEY: import.meta.env.VITE_SM4_KEY || "0123456789abcdeffedcba9876543210",

  // 是否启用加密（可以通过环境变量控制）
  ENABLE_ENCRYPTION: import.meta.env.VITE_ENABLE_ENCRYPTION !== "false",

  // 需要加密的接口白名单（只有这些接口会被加密）
  ENCRYPT_WHITELIST: [
    "/api/user/login",
    "/api/user/register",
    "/api/sensitive-data",
    // 可以根据需要添加更多接口
    "/easyzhipin-admin/login/generateCode"
  ],

  // 不需要加密的接口黑名单（这些接口不会被加密）
  ENCRYPT_BLACKLIST: [
    "/api/public",
    "/api/health",
    "/api/version",
    "/refresh-token"
    // 可以根据需要添加更多接口
  ]
};

/**
 * 检查接口是否需要加密
 * @param url 接口URL
 * @returns 是否需要加密
 */
export function shouldEncrypt(url: string): boolean {
  // 如果全局禁用加密，直接返回false
  if (!CRYPTO_CONFIG.ENABLE_ENCRYPTION) {
    return false;
  }

  // 检查黑名单
  if (CRYPTO_CONFIG.ENCRYPT_BLACKLIST.some(pattern => url.includes(pattern))) {
    return false;
  }

  // 如果有白名单，只加密白名单中的接口
  if (CRYPTO_CONFIG.ENCRYPT_WHITELIST.length > 0) {
    return CRYPTO_CONFIG.ENCRYPT_WHITELIST.some(pattern =>
      url.includes(pattern)
    );
  }

  // 默认加密所有接口（除了黑名单）
  return true;
}
