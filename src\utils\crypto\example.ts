/**
 * 国密加密使用示例
 * 演示如何在项目中使用国密加密功能
 */

import { http } from "@/utils/http";
import SMCrypto from "./index";

/**
 * 示例1: 自动加密的API请求
 * 当接口在加密白名单中时，请求数据会自动加密，响应数据会自动解密
 */
export async function loginWithEncryption(username: string, password: string) {
  try {
    // 这个请求会自动加密（如果/api/user/login在白名单中）
    const response = await http.post("/api/user/login", {
      username,
      password
    });

    console.log("登录响应（已自动解密）:", response);
    return response;
  } catch (error) {
    console.error("登录失败:", error);
    throw error;
  }
}

/**
 * 示例2: 手动控制加密
 * 通过设置请求头来控制是否加密
 */
export async function sendDataWithManualEncryption(data: any, encrypt = true) {
  try {
    const response = await http.post("/api/data", data, {
      headers: {
        // 设置为false可以跳过自动加密
        "X-Encrypt": encrypt ? "true" : "false"
      }
    });

    return response;
  } catch (error) {
    console.error("发送数据失败:", error);
    throw error;
  }
}

/**
 * 示例3: 直接使用加密工具类
 * 在某些场景下可能需要手动加密/解密数据
 */
export function manualEncryptionExample() {
  const originalData = {
    username: "testuser",
    password: "testpassword",
    email: "<EMAIL>"
  };

  console.log("原始数据:", originalData);

  try {
    // SM4 加密（推荐用于数据传输）
    const encryptedData = SMCrypto.encryptRequestData(originalData);
    console.log("SM4加密后:", encryptedData);

    // SM4 解密
    const decryptedData = SMCrypto.decryptResponseData(encryptedData);
    console.log("SM4解密后:", decryptedData);

    // SM3 哈希（用于数据完整性校验）
    const hashValue = SMCrypto.sm3Hash(JSON.stringify(originalData));
    console.log("SM3哈希值:", hashValue);

    // SM2 加密（用于密钥交换或重要数据加密）
    const sm2Encrypted = SMCrypto.sm2Encrypt(JSON.stringify(originalData));
    console.log("SM2加密后:", sm2Encrypted);

    // SM2 解密
    const sm2Decrypted = SMCrypto.sm2Decrypt(sm2Encrypted);
    console.log("SM2解密后:", JSON.parse(sm2Decrypted));
  } catch (error) {
    console.error("加密操作失败:", error);
  }
}

/**
 * 示例4: 文件上传时的加密处理
 */
export async function uploadFileWithEncryption(file: File) {
  try {
    // 读取文件内容
    const fileContent = await file.text();

    // 加密文件内容
    const encryptedContent = SMCrypto.sm4Encrypt(fileContent);

    // 创建新的加密文件
    const encryptedFile = new Blob([encryptedContent], { type: "text/plain" });

    // 上传加密文件
    const formData = new FormData();
    formData.append("file", encryptedFile, `encrypted_${file.name}`);
    formData.append("encrypted", "true");

    const response = await http.post("/api/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        // 文件上传通常不需要再次加密整个请求体
        "X-Encrypt": "false"
      }
    });

    return response;
  } catch (error) {
    console.error("文件上传失败:", error);
    throw error;
  }
}

/**
 * 示例5: 敏感配置信息的本地存储加密
 */
export class SecureStorage {
  private static readonly STORAGE_KEY = "secure_config";

  /**
   * 安全存储配置信息
   */
  static setSecureConfig(config: any) {
    try {
      const encryptedConfig = SMCrypto.sm4Encrypt(JSON.stringify(config));
      localStorage.setItem(this.STORAGE_KEY, encryptedConfig);
    } catch (error) {
      console.error("配置加密存储失败:", error);
    }
  }

  /**
   * 安全读取配置信息
   */
  static getSecureConfig(): any {
    try {
      const encryptedConfig = localStorage.getItem(this.STORAGE_KEY);
      if (!encryptedConfig) return null;

      const decryptedConfig = SMCrypto.sm4Decrypt(encryptedConfig);
      return JSON.parse(decryptedConfig);
    } catch (error) {
      console.error("配置解密读取失败:", error);
      return null;
    }
  }

  /**
   * 清除安全配置
   */
  static clearSecureConfig() {
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

// 导出所有示例函数
export default {
  loginWithEncryption,
  sendDataWithManualEncryption,
  manualEncryptionExample,
  uploadFileWithEncryption,
  SecureStorage
};
