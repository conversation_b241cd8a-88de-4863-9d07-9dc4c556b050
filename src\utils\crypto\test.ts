/**
 * 国密加密功能测试
 * 用于验证加密解密功能是否正常工作
 */

import SMCrypto from "./index";
import { shouldEncrypt } from "./config";

/**
 * 测试基础加密解密功能
 */
export function testBasicCrypto() {
  console.log("=== 开始测试基础加密解密功能 ===");
  
  const testData = {
    username: "admin",
    password: "123456",
    timestamp: Date.now()
  };
  
  try {
    // 测试 SM4 加密解密
    console.log("1. 测试 SM4 加密解密");
    const sm4Encrypted = SMCrypto.encryptRequestData(testData);
    console.log("SM4 加密结果:", sm4Encrypted);
    
    const sm4Decrypted = SMCrypto.decryptResponseData(sm4Encrypted);
    console.log("SM4 解密结果:", sm4Decrypted);
    
    const isEqual = JSON.stringify(testData) === JSON.stringify(sm4Decrypted);
    console.log("SM4 加密解密是否成功:", isEqual);
    
    // 测试 SM3 哈希
    console.log("\n2. 测试 SM3 哈希");
    const hashValue = SMCrypto.sm3Hash(JSON.stringify(testData));
    console.log("SM3 哈希值:", hashValue);
    
    // 测试 SM2 加密解密
    console.log("\n3. 测试 SM2 加密解密");
    const dataString = JSON.stringify(testData);
    const sm2Encrypted = SMCrypto.sm2Encrypt(dataString);
    console.log("SM2 加密结果:", sm2Encrypted);
    
    const sm2Decrypted = SMCrypto.sm2Decrypt(sm2Encrypted);
    console.log("SM2 解密结果:", sm2Decrypted);
    
    const sm2IsEqual = dataString === sm2Decrypted;
    console.log("SM2 加密解密是否成功:", sm2IsEqual);
    
    console.log("=== 基础加密解密功能测试完成 ===\n");
    return { sm4: isEqual, sm2: sm2IsEqual };
    
  } catch (error) {
    console.error("基础加密解密测试失败:", error);
    return { sm4: false, sm2: false };
  }
}

/**
 * 测试接口加密策略
 */
export function testEncryptionStrategy() {
  console.log("=== 开始测试接口加密策略 ===");
  
  const testUrls = [
    // 应该加密的接口
    "/login",
    "/easyzhipin-admin/user/profile",
    "/api/user/register",
    
    // 不应该加密的接口
    "/easyzhipin-admin/login/generateCode",
    "/refresh-token",
    "/api/public/data",
    "/get-async-routes",
    
    // 边界情况
    "/api/health",
    "/platform-config.json"
  ];
  
  testUrls.forEach(url => {
    const shouldEnc = shouldEncrypt(url);
    console.log(`接口 ${url} 是否需要加密: ${shouldEnc}`);
  });
  
  console.log("=== 接口加密策略测试完成 ===\n");
}

/**
 * 测试错误处理
 */
export function testErrorHandling() {
  console.log("=== 开始测试错误处理 ===");
  
  try {
    // 测试无效数据加密
    console.log("1. 测试无效数据加密");
    const invalidData = undefined;
    const result1 = SMCrypto.encryptRequestData(invalidData);
    console.log("无效数据加密结果:", result1);
  } catch (error) {
    console.log("无效数据加密错误（预期）:", error.message);
  }
  
  try {
    // 测试无效数据解密
    console.log("\n2. 测试无效数据解密");
    const invalidEncrypted = "invalid_encrypted_data";
    const result2 = SMCrypto.decryptResponseData(invalidEncrypted);
    console.log("无效数据解密结果:", result2);
  } catch (error) {
    console.log("无效数据解密错误（预期）:", error.message);
  }
  
  try {
    // 测试空字符串解密
    console.log("\n3. 测试空字符串解密");
    const emptyEncrypted = "";
    const result3 = SMCrypto.sm4Decrypt(emptyEncrypted);
    console.log("空字符串解密结果:", result3);
  } catch (error) {
    console.log("空字符串解密错误（预期）:", error.message);
  }
  
  console.log("=== 错误处理测试完成 ===\n");
}

/**
 * 性能测试
 */
export function testPerformance() {
  console.log("=== 开始性能测试 ===");
  
  const testData = {
    username: "admin",
    password: "123456",
    data: "a".repeat(1000) // 1KB 数据
  };
  
  const iterations = 100;
  
  // SM4 性能测试
  console.log(`1. SM4 加密解密性能测试 (${iterations} 次)`);
  const sm4StartTime = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    const encrypted = SMCrypto.encryptRequestData(testData);
    SMCrypto.decryptResponseData(encrypted);
  }
  
  const sm4EndTime = performance.now();
  const sm4Duration = sm4EndTime - sm4StartTime;
  console.log(`SM4 总耗时: ${sm4Duration.toFixed(2)}ms`);
  console.log(`SM4 平均耗时: ${(sm4Duration / iterations).toFixed(2)}ms/次`);
  
  // SM2 性能测试
  console.log(`\n2. SM2 加密解密性能测试 (${iterations} 次)`);
  const sm2StartTime = performance.now();
  const dataString = JSON.stringify(testData);
  
  for (let i = 0; i < iterations; i++) {
    const encrypted = SMCrypto.sm2Encrypt(dataString);
    SMCrypto.sm2Decrypt(encrypted);
  }
  
  const sm2EndTime = performance.now();
  const sm2Duration = sm2EndTime - sm2StartTime;
  console.log(`SM2 总耗时: ${sm2Duration.toFixed(2)}ms`);
  console.log(`SM2 平均耗时: ${(sm2Duration / iterations).toFixed(2)}ms/次`);
  
  console.log("=== 性能测试完成 ===\n");
  
  return {
    sm4: {
      total: sm4Duration,
      average: sm4Duration / iterations
    },
    sm2: {
      total: sm2Duration,
      average: sm2Duration / iterations
    }
  };
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log("🚀 开始运行国密加密功能完整测试");
  console.log("=====================================");
  
  const results = {
    basic: testBasicCrypto(),
    strategy: testEncryptionStrategy(),
    error: testErrorHandling(),
    performance: testPerformance()
  };
  
  console.log("=====================================");
  console.log("✅ 所有测试完成");
  console.log("测试结果摘要:");
  console.log("- SM4 加密解密:", results.basic.sm4 ? "✅ 通过" : "❌ 失败");
  console.log("- SM2 加密解密:", results.basic.sm2 ? "✅ 通过" : "❌ 失败");
  console.log(`- SM4 平均性能: ${results.performance.sm4.average.toFixed(2)}ms/次`);
  console.log(`- SM2 平均性能: ${results.performance.sm2.average.toFixed(2)}ms/次`);
  
  return results;
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，避免影响应用启动
  setTimeout(() => {
    console.log("🔧 开发环境检测到，自动运行加密功能测试");
    runAllTests();
  }, 2000);
}

export default {
  testBasicCrypto,
  testEncryptionStrategy,
  testErrorHandling,
  testPerformance,
  runAllTests
};
